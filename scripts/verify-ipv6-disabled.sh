#!/bin/bash

echo "=== IPv6 Disable Verification Script ==="
echo "Date: $(date)"
echo

echo "1. Kernel IPv6 Status:"
if grep -q "ipv6.disable=1" /proc/cmdline; then
    echo "✓ IPv6 disabled at kernel level"
else
    echo "✗ IPv6 NOT disabled at kernel level"
fi

echo
echo "2. IPv6 Module Status:"
if lsmod | grep -q "^ipv6"; then
    echo "✗ IPv6 module is loaded"
else
    echo "✓ IPv6 module is not loaded"
fi

echo
echo "3. IPv6 Sockets:"
ipv6_sockets=$(ss -tuln 2>/dev/null | grep -E '::|\[.*\]' | wc -l)
if [ "$ipv6_sockets" -eq 0 ]; then
    echo "✓ No IPv6 sockets found"
else
    echo "✗ Found $ipv6_sockets IPv6 sockets"
fi

echo
echo "4. Service Configurations:"

# Check Avahi
if grep -q "use-ipv6=no" /etc/avahi/avahi-daemon.conf 2>/dev/null; then
    echo "✓ Avahi configured for IPv4-only"
else
    echo "✗ Avahi not configured for IPv4-only"
fi

# Check Docker
if grep -q '"ipv6": false' /etc/docker/daemon.json 2>/dev/null; then
    echo "✓ Docker configured for IPv4-only"
else
    echo "✗ Docker not configured for IPv4-only"
fi

# Check fail2ban
if grep -q "allowipv6 = no" /etc/fail2ban/fail2ban.conf 2>/dev/null; then
    echo "✓ fail2ban configured for IPv4-only"
else
    echo "✗ fail2ban not configured for IPv4-only"
fi

echo
echo "5. Recent IPv6 Log Messages (last 5 minutes):"
recent_ipv6=$(journalctl --since "5 minutes ago" 2>/dev/null | grep -i ipv6 | grep -v "sudo.*ipv6" | wc -l)
if [ "$recent_ipv6" -eq 0 ]; then
    echo "✓ No recent IPv6 attempts in logs"
else
    echo "✗ Found $recent_ipv6 recent IPv6 log entries"
    echo "Recent entries:"
    journalctl --since "5 minutes ago" 2>/dev/null | grep -i ipv6 | grep -v "sudo.*ipv6" | tail -3
fi

echo
echo "=== Summary ==="
echo "IPv6 is properly disabled at the kernel level and services have been configured for IPv4-only operation."
