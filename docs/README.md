# System Documentation

**Dell Precision 5560** - Professional Linux workstation with enterprise-grade security and performance optimization.

## 🚀 Quick Start

### **Essential Links**
- **[Installation Guide](getting-started/installation.md)** - Complete setup instructions
- **[System CLI Guide](user-guides/system-cli.md)** - Primary tool for system management
- **[Thermal Management](user-guides/thermal-management.md)** - 🔥 **CRITICAL**: Temperature monitoring
- **[Time Synchronization](technical-reference/time-synchronization.md)** - 🕐 **NEW**: Eliminates time drift issues
- **[Virtualization Optimization](technical-reference/virtualization-optimization.md)** - 🚀 **NEW**: Enterprise VM performance
- **[Current Status](dashboard.md)** - Real-time system status and actions

## 📚 Documentation Sections

### **User Guides**
- **[System CLI](user-guides/system-cli.md)** - Complete command reference and usage
- **[Authentication](user-guides/authentication.md)** - Security setup and management
- **[Monitoring](user-guides/monitoring.md)** - System health and performance tracking
- **[Thermal Management](user-guides/thermal-management.md)** - Temperature control and optimization

### **Technical Reference**
- **[Hardware Specifications](technical-reference/hardware-specifications.md)** - Complete hardware details
- **[Software Specifications](technical-reference/software-specifications.md)** - Software stack and packages
- **[System Configuration](technical-reference/system-configuration.md)** - Configuration reference
- **[IPv6 Disable Configuration](technical-reference/ipv6-disable-configuration.md)** - 🆕 **NEW**: Complete IPv6 disable setup
- **[Security Audit](technical-reference/security-audit.md)** - Security assessment and hardening

### **Quick Reference**
- **[Network Commands](quick-reference/network-commands.md)** - 🆕 **NEW**: IPv6 verification and network troubleshooting
- **[Authentication Commands](quick-reference/authentication-cheat-sheet.md)** - Security command reference
- **[Time Sync Commands](quick-reference/time-sync-commands.md)** - Time synchronization reference

### **Development**
- **[Contributing](development/contributing.md)** - Development guidelines and workflow
- **[Architecture](development/architecture.md)** - System design and structure
- **[API Reference](development/api-reference.md)** - Code documentation

## 🎯 Current Status

**System Health**: 8.8/10 (Excellent) | **Security Score**: 9.5/10 | **Last Updated**: June 2025

### **Immediate Attention Required**
- 🔥 **Thermal Management**: CPU running hot (60-64°C idle) - [See thermal guide](user-guides/thermal-management.md)
- 💾 **Storage**: Primary drive 91% full - Run `system-cli maintenance cleanup`

### **System Highlights**
- ✅ **Security**: Enterprise-grade with fingerprint auth and dual keyring
- ✅ **Performance**: Optimized power management and thermal control
- ✅ **Automation**: Comprehensive monitoring and maintenance tools
- ✅ **Documentation**: Complete technical and user documentation

## 🔧 Essential Commands

```bash
# Thermal monitoring (CRITICAL)
./scripts/thermal_monitor.sh

# System management
system-cli status
system-cli security audit
system-cli maintenance cleanup

# Authentication
system-cli keyring status
system-cli auth test
```

## 📁 Project Structure

```
docs/
├── getting-started/          # Installation and setup
├── user-guides/             # User documentation and guides
├── technical-reference/     # Technical specifications
├── development/             # Developer resources
├── planning/                # Current recommendations and planning
└── archive/                 # Historical documentation
```

## 🔗 Quick Links

- **[GitHub Repository](https://github.com/bcherrington/cursor-system)** - Source code and issues
- **[System Overview](system-overview.md)** - Complete system architecture
- **[Current Recommendations](planning/current-recommendations.md)** - Active action items
