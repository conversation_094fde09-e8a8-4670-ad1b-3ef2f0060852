# Network Configuration Quick Reference

**System**: Dell Precision 5560 - Ubuntu 24.04 LTS  
**Last Updated**: June 19, 2025

---

## 🌐 IPv6 Disable Verification

### **Quick Status Check**
```bash
# One-liner IPv6 status check
grep -q "ipv6.disable=1" /proc/cmdline && echo "✅ IPv6 disabled" || echo "❌ IPv6 enabled"

# Check for IPv6 sockets
ss -tuln | grep -E '::|\[.*\]' && echo "❌ IPv6 sockets found" || echo "✅ No IPv6 sockets"

# Check IPv6 module
lsmod | grep -q "^ipv6" && echo "❌ IPv6 module loaded" || echo "✅ IPv6 module not loaded"
```

### **Service Configuration Check**
```bash
# Avahi IPv6 status
grep "use-ipv6" /etc/avahi/avahi-daemon.conf

# Docker IPv6 status
grep "ipv6" /etc/docker/daemon.json

# fail2ban IPv6 status
grep "allowipv6" /etc/fail2ban/fail2ban.conf
```

### **Log Monitoring**
```bash
# Check recent IPv6 attempts
journalctl --since "1 hour ago" | grep -i ipv6

# Monitor IPv6 attempts in real-time
journalctl -f | grep -i ipv6

# Check for IPv6 errors
journalctl --since "today" | grep -i "ipv6.*error\|ipv6.*failed"
```

---

## 🔧 Network Troubleshooting

### **Basic Network Status**
```bash
# Network interfaces
ip addr show

# Routing table
ip route show

# DNS configuration
systemd-resolve --status

# Network connectivity
ping -c 4 *******
```

### **DNS over HTTPS Status**
```bash
# Cloudflared status
systemctl status cloudflared-dns

# DNS resolution test
nslookup google.com 127.0.0.1

# DoH verification
dig @127.0.0.1 -p 5053 google.com
```

### **Firewall Status**
```bash
# UFW status
sudo ufw status verbose

# fail2ban status
sudo fail2ban-client status

# Active connections
ss -tuln
```

---

## 📊 Network Performance

### **Bandwidth Testing**
```bash
# Speed test (install speedtest-cli first)
speedtest-cli

# Local network performance
iperf3 -c <server_ip>

# Ping statistics
ping -c 10 ******* | tail -1
```

### **Connection Monitoring**
```bash
# Active connections
netstat -tuln

# Network statistics
cat /proc/net/dev

# Interface statistics
ip -s link show
```

---

## 🛠️ Configuration Files

### **Key Network Configuration Files**
```bash
# Network interfaces
/etc/netplan/          # Netplan configuration (if used)
/etc/NetworkManager/   # NetworkManager configuration

# DNS configuration
/etc/systemd/resolved.conf
/etc/resolv.conf

# IPv6 disable configuration
/etc/modprobe.d/blacklist-ipv6.conf
/etc/default/grub      # Kernel parameters

# Service configurations
/etc/avahi/avahi-daemon.conf
/etc/docker/daemon.json
/etc/fail2ban/fail2ban.conf
```

### **Backup Commands**
```bash
# Backup network configurations
sudo cp /etc/avahi/avahi-daemon.conf /etc/avahi/avahi-daemon.conf.backup
sudo cp /etc/docker/daemon.json /etc/docker/daemon.json.backup
sudo cp /etc/fail2ban/fail2ban.conf /etc/fail2ban/fail2ban.conf.backup
```

---

## 🔄 Service Management

### **Network Service Control**
```bash
# NetworkManager
sudo systemctl restart NetworkManager

# DNS services
sudo systemctl restart systemd-resolved
sudo systemctl restart cloudflared-dns

# IPv4-only services
sudo systemctl restart avahi-daemon
sudo systemctl restart docker
sudo systemctl restart fail2ban
```

### **Service Status Check**
```bash
# All network services
systemctl status NetworkManager systemd-resolved cloudflared-dns avahi-daemon

# Docker networking
docker network ls
docker network inspect bridge
```

---

## 🚨 Emergency Procedures

### **Network Recovery**
```bash
# Reset NetworkManager
sudo systemctl stop NetworkManager
sudo systemctl start NetworkManager

# Flush DNS cache
sudo systemd-resolve --flush-caches

# Restart all network services
sudo systemctl restart NetworkManager systemd-resolved cloudflared-dns
```

### **IPv6 Re-enable (Emergency)**
```bash
# WARNING: Only if IPv6 is absolutely required
# 1. Edit GRUB configuration
sudo sed -i 's/ipv6.disable=1//' /etc/default/grub
sudo update-grub

# 2. Remove module blacklist
sudo rm /etc/modprobe.d/blacklist-ipv6.conf

# 3. Restore service configurations from backups
sudo cp /etc/avahi/avahi-daemon.conf.backup /etc/avahi/avahi-daemon.conf
sudo cp /etc/docker/daemon.json.backup /etc/docker/daemon.json
sudo cp /etc/fail2ban/fail2ban.conf.backup /etc/fail2ban/fail2ban.conf

# 4. Reboot required
sudo reboot
```

---

## 📋 Verification Checklist

### **IPv6 Disable Verification**
- [ ] Kernel parameter `ipv6.disable=1` present in `/proc/cmdline`
- [ ] No IPv6 module loaded (`lsmod | grep ipv6`)
- [ ] No IPv6 sockets active (`ss -tuln | grep ::`)
- [ ] Avahi configured with `use-ipv6=no`
- [ ] Docker configured with `"ipv6": false`
- [ ] fail2ban configured with `allowipv6 = no`
- [ ] No recent IPv6 log messages
- [ ] Module blacklist file exists

### **Network Functionality**
- [ ] IPv4 connectivity working
- [ ] DNS resolution working
- [ ] DoH proxy active
- [ ] Firewall active
- [ ] All services running

---

*Quick reference for network configuration and IPv6 disable verification on Dell Precision 5560.*
