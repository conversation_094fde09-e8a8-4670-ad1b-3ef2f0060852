# Time Synchronization Quick Reference

**Quick commands for monitoring and managing time synchronization**

---

## 🔍 Status Checks

### **Overall Time Status**
```bash
# Complete time configuration overview
timedatectl status

# Expected output should show:
# - System clock synchronized: yes
# - NTP service: active  
# - RTC in local TZ: no  ← CRITICAL
```

### **NTP Service Health**
```bash
# Check NTP service status
systemctl status systemd-timesyncd

# View recent sync activity
journalctl -u systemd-timesyncd --since "1 hour ago"

# Check sync details
timedatectl timesync-status
```

### **Time Accuracy Check**
```bash
# Compare system time vs RTC
echo "System: $(date)"
echo "RTC:    $(sudo hwclock --show)"

# Check for drift (should be minimal)
timedatectl timesync-status | grep "Offset:"
```

---

## 🔧 Configuration Commands

### **Fix RTC Configuration (if needed)**
```bash
# Set RTC to UTC mode (CRITICAL for proper sync)
sudo timedatectl set-local-rtc 0

# Verify the change
timedatectl status | grep "RTC in local TZ"
# Should show: RTC in local TZ: no
```

### **Restart Time Services**
```bash
# Restart NTP service
sudo systemctl restart systemd-timesyncd

# Force immediate sync
sudo systemctl stop systemd-timesyncd
sudo ntpdate -s time.nist.gov
sudo systemctl start systemd-timesyncd
```

---

## 🖥️ VM Time Management

### **Check VM Clock Configuration**
```bash
# View VM clock settings
virsh dumpxml win11 | grep -A 6 "clock"

# Check VM is running
virsh list
```

### **Restart VM with New Clock Config**
```bash
# Graceful shutdown and restart
virsh shutdown win11
sleep 10
virsh start win11
```

---

## 📊 Monitoring Commands

### **Real-Time Monitoring**
```bash
# Watch time sync status
watch -n 5 'timedatectl timesync-status'

# Monitor time drift
watch -n 10 'echo "System: $(date)" && echo "RTC:    $(sudo hwclock --show)"'

# Watch NTP service logs
journalctl -u systemd-timesyncd -f
```

### **Health Check Script**
```bash
#!/bin/bash
echo "=== TIME SYNC HEALTH CHECK ==="
echo "System Time: $(date)"
echo "RTC Time:    $(sudo hwclock --show)"
echo "NTP Status:  $(timedatectl show --property=NTPSynchronized --value)"
echo "RTC Mode:    $(timedatectl show --property=LocalRTC --value)"

DRIFT=$(timedatectl timesync-status 2>/dev/null | grep "Offset:" | awk '{print $2}' || echo "unknown")
echo "Time Offset: $DRIFT"

if [ "$DRIFT" != "0" ] && [ "$DRIFT" != "unknown" ]; then
    echo "⚠️  Time drift detected: $DRIFT"
else
    echo "✅ Time synchronization healthy"
fi
```

---

## 🚨 Emergency Recovery

### **If Time Becomes Severely Out of Sync**
```bash
# Emergency time sync
sudo systemctl stop systemd-timesyncd
sudo ntpdate -s time.nist.gov
sudo hwclock --systohc
sudo systemctl start systemd-timesyncd
```

### **If NTP Service Fails**
```bash
# Reset NTP configuration
sudo systemctl stop systemd-timesyncd
sudo rm -f /var/lib/systemd/timesync/clock
sudo systemctl start systemd-timesyncd
```

### **If RTC Gets Corrupted**
```bash
# Reset RTC from system time
sudo hwclock --systohc --utc
sudo timedatectl set-local-rtc 0
```

---

## 📁 Configuration Files

### **Main NTP Configuration**
```bash
# Edit NTP settings
sudo nano /etc/systemd/timesyncd.conf

# Current optimized settings:
# NTP=za.pool.ntp.org 0.za.pool.ntp.org 1.za.pool.ntp.org
# FallbackNTP=ntp.ubuntu.com time.nist.gov
# PollIntervalMinSec=16
# PollIntervalMaxSec=1024
```

### **VM Clock Configuration**
```bash
# Backup VM config
virsh dumpxml win11 > win11-backup.xml

# Edit VM configuration
virsh edit win11

# Key clock settings:
# <clock offset='utc' adjustment='reset' basis='utc'>
#   <timer name='hypervclock' present='yes'/>
#   <timer name='tsc' present='yes' mode='native'/>
# </clock>
```

---

## ✅ Success Indicators

### **Healthy System Shows**
- ✅ `System clock synchronized: yes`
- ✅ `NTP service: active`
- ✅ `RTC in local TZ: no`
- ✅ Time offset: 0 or very small (< 1 second)
- ✅ NTP server contacted successfully
- ✅ VM time matches host time

### **Warning Signs**
- ❌ `System clock synchronized: no`
- ❌ `RTC in local TZ: yes`
- ❌ Large time offset (> 5 seconds)
- ❌ NTP service inactive or failed
- ❌ VM time significantly different from host

---

*Keep this reference handy for quick time synchronization troubleshooting and maintenance.*
