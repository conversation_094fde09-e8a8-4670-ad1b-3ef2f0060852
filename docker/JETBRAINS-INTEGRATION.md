# JetBrains IDE Development Container Integration

This template provides seamless integration between JetBrains IDEs (PyCharm, WebStorm, GoLand) and Docker development containers, ensuring consistent development environments across teams.

## 🎯 **Template Overview**

### **Key Features**
✅ **Native JetBrains Integration** - Remote interpreter and debugging support  
✅ **Optimized Performance** - Fast file sync and responsive IDE experience  
✅ **Pre-configured Settings** - IDE settings and tool configurations included  
✅ **Hot Reload Support** - Automatic code reloading during development  
✅ **Multi-Service Setup** - Database, cache, and supporting services included  

### **Supported IDEs**
- **PyCharm Professional/Community** - Python development
- **WebStorm** - Node.js/TypeScript development  
- **GoLand** - Go development
- **IntelliJ IDEA Ultimate** - Multi-language support

## 🚀 **Quick Start**

### **1. Initialize Development Container**
```bash
# Initialize Python project
./docker/devcontainer-manager.sh init python my-python-app

# Initialize Node.js project  
./docker/devcontainer-manager.sh init node my-node-app

# Initialize Go project
./docker/devcontainer-manager.sh init golang my-go-app
```

### **2. Start Development Environment**
```bash
# Start the development environment
./docker/devcontainer-manager.sh start my-python-app

# Check status
./docker/devcontainer-manager.sh status my-python-app
```

### **3. Configure JetBrains IDE**
```bash
# Get IDE setup instructions
./docker/devcontainer-manager.sh jetbrains-setup my-python-app

# Follow the displayed instructions for your specific IDE
```

## 🔧 **JetBrains IDE Configuration**

### **PyCharm Setup (Python Projects)**

1. **Open Project**
   - File → Open → Select your project directory

2. **Configure Remote Interpreter**
   - Settings → Project → Python Interpreter
   - Add Interpreter → Docker Compose
   - Configuration file: `.devcontainer/docker-compose.dev.yml`
   - Service: `{project-name}-dev`
   - Python interpreter path: `/usr/local/bin/python`

3. **Configure Debugging**
   - Run/Debug Configurations → Add → Python Debug Server
   - Host: `localhost`
   - Port: `5678`
   - Path mappings: `/workspace` → `{project-root}`

### **WebStorm Setup (Node.js Projects)**

1. **Open Project**
   - File → Open → Select your project directory

2. **Configure Node.js Interpreter**
   - Settings → Languages & Frameworks → Node.js
   - Node interpreter: Docker Compose
   - Configuration file: `.devcontainer/docker-compose.dev.yml`
   - Service: `{project-name}-dev`
   - Node.js interpreter path: `/usr/local/bin/node`

3. **Configure Debugging**
   - Run/Debug Configurations → Add → Node.js
   - Node interpreter: (use configured Docker Compose)
   - Working directory: `/workspace`
   - JavaScript file: `index.js` or `app.js`

### **GoLand Setup (Go Projects)**

1. **Open Project**
   - File → Open → Select your project directory

2. **Configure Go Environment**
   - Settings → Go → GOROOT
   - GOROOT: Docker Compose
   - Configuration file: `.devcontainer/docker-compose.dev.yml`
   - Service: `{project-name}-dev`
   - Go interpreter path: `/usr/local/go/bin/go`

## 📁 **Template Structure**

### **Python Development Container**
```
.devcontainer/
├── .devcontainer.json              # VS Code dev container config
├── docker-compose.dev.yml         # Multi-service development setup
├── Dockerfile.dev                 # Development-optimized container
├── jetbrains-settings/             # Pre-configured IDE settings
│   └── PyCharm2023.3/
│       └── options/
│           ├── ide.general.xml     # General IDE settings
│           └── python.xml          # Python-specific settings
└── .env                           # Project environment variables
```

### **Services Included**
- **Main Application Container** - Your development environment
- **PostgreSQL** - Database service with persistent storage
- **Redis** - Caching and session storage
- **Elasticsearch** - Search functionality (optional profile)

## 🔍 **Development Workflow**

### **Daily Development**
```bash
# Start your development day
./docker/devcontainer-manager.sh start my-project

# Open shell for command-line work
./docker/devcontainer-manager.sh shell my-project

# View logs
./docker/devcontainer-manager.sh logs my-project --follow

# Stop when done
./docker/devcontainer-manager.sh stop my-project
```

### **Debugging Workflow**
1. **Set Breakpoints** in JetBrains IDE
2. **Start Debug Server** in container:
   ```bash
   # Python
   python -m debugpy --listen 0.0.0.0:5678 --wait-for-client app.py
   
   # Node.js
   node --inspect=0.0.0.0:9229 app.js
   ```
3. **Attach Debugger** from IDE
4. **Debug Normally** with full IDE features

### **Hot Reload Development**
```bash
# Python (FastAPI/Flask)
hot-reload

# Node.js
npm run dev

# Go
air -c .air.toml
```

## ⚡ **Performance Optimizations**

### **File Synchronization**
- **Cached Volumes** - Source code with optimized consistency
- **Delegated Volumes** - Cache directories for better performance
- **Bind Mounts** - IDE settings and SSH keys

### **Container Optimizations**
- **BuildKit Cache Mounts** - Persistent dependency caches
- **Multi-stage Builds** - Optimized development images
- **Resource Limits** - Balanced CPU and memory allocation

### **IDE Optimizations**
- **Remote File Caching** - Faster file operations
- **Optimized Indexing** - Reduced indexing overhead
- **Efficient Watching** - Smart file change detection

## 🛠 **Customization Options**

### **Environment Variables**
```bash
# .devcontainer/.env
PROJECT_NAME=my-project
ENVIRONMENT=development
DEBUG=true
DATABASE_URL=********************************************/devdb
```

### **Port Configuration**
```yaml
# docker-compose.dev.yml
ports:
  - "8000:8000"   # Application
  - "5678:5678"   # Python debugger
  - "9229:9229"   # Node.js debugger
  - "5432:5432"   # PostgreSQL
```

### **Volume Mounts**
```yaml
volumes:
  # Source code (cached for performance)
  - type: bind
    source: .
    target: /workspace
    consistency: cached
  
  # IDE settings (persistent)
  - type: bind
    source: ./.devcontainer/jetbrains-settings
    target: /home/<USER>/.config/JetBrains
    consistency: cached
```

## 📊 **Performance Expectations**

### **Container Startup**
- **Cold Start**: 15-30 seconds (first time)
- **Warm Start**: 5-10 seconds (subsequent starts)
- **Service Ready**: 30-45 seconds (with database)

### **IDE Performance**
- **Code Completion**: Near-native responsiveness
- **File Operations**: Optimized with caching
- **Debugging**: Full-featured remote debugging
- **Hot Reload**: 1-3 seconds for code changes

### **Resource Usage**
- **Memory**: 2-4GB per development container
- **CPU**: 0.5-2.0 cores during active development
- **Storage**: 1-3GB for development tools and dependencies

## 🔒 **Security Features**

### **Container Security**
- **Non-root User** - Development user with sudo access
- **Limited Privileges** - Minimal required permissions
- **Isolated Network** - Dedicated development network

### **Development Security**
- **SSH Key Mounting** - Read-only SSH key access
- **Git Configuration** - Secure git operations
- **Environment Isolation** - Separated from host system

## 🚀 **Advanced Features**

### **Multi-Project Support**
```bash
# Work on multiple projects simultaneously
./docker/devcontainer-manager.sh start project-a
./docker/devcontainer-manager.sh start project-b
./docker/devcontainer-manager.sh start project-c
```

### **Team Collaboration**
- **Shared Settings** - Consistent IDE configuration across team
- **Version Control** - Dev container config in git
- **Reproducible Environments** - Same setup for all developers

### **CI/CD Integration**
- **Testing Containers** - Same environment for testing
- **Build Consistency** - Identical build environment
- **Deployment Parity** - Development matches production

## 🎉 **Benefits Summary**

### **For Developers**
✅ **Consistent Environment** - Same setup across all machines  
✅ **Fast Setup** - New developers productive in minutes  
✅ **Full IDE Features** - Native debugging, completion, refactoring  
✅ **Isolated Dependencies** - No conflicts with host system  

### **For Teams**
✅ **Standardized Tooling** - Everyone uses same tools and versions  
✅ **Shared Configuration** - IDE settings and code styles in version control  
✅ **Onboarding Speed** - New team members productive immediately  
✅ **Reduced Support** - Fewer "works on my machine" issues  

### **For Projects**
✅ **Reproducible Builds** - Consistent environment for development and CI  
✅ **Easy Maintenance** - Update development environment for entire team  
✅ **Technology Flexibility** - Easy to try new tools and versions  
✅ **Documentation** - Development setup documented as code  

---

*JetBrains IDE integration template optimized for 16-core development systems and team collaboration*
