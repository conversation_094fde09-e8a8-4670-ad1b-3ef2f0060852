#!/bin/bash

# Development Container Manager
# Utility for managing JetBrains IDE development containers

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEMPLATES_DIR="$SCRIPT_DIR/templates/devcontainer"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show usage information
show_usage() {
    cat << EOF
🚀 Development Container Manager

Usage: $0 <command> [options]

Commands:
  init <type> [project_name]       Initialize dev container for project
  start [project_name]             Start development environment
  stop [project_name]              Stop development environment
  restart [project_name]           Restart development environment
  logs [project_name]              Show development container logs
  shell [project_name]             Open shell in development container
  status [project_name]            Show development environment status
  clean [project_name]             Clean up development environment
  jetbrains-setup [project_name]   Configure JetBrains IDE integration
  list                             List available templates
  help                             Show this help message

Container Types:
  python                           Python development with PyCharm support
  node                             Node.js development with WebStorm support
  golang                           Go development with GoLand support

Examples:
  $0 init python my-python-app
  $0 start my-python-app
  $0 jetbrains-setup my-python-app
  $0 shell my-python-app
  $0 logs my-python-app

Options:
  --rebuild                        Force rebuild of containers
  --no-cache                       Build without using cache
  --detach                         Run in detached mode
  --follow                         Follow logs output

EOF
}

# Detect project type
detect_project_type() {
    local project_dir="${1:-.}"
    
    if [[ -f "$project_dir/pyproject.toml" ]] || [[ -f "$project_dir/requirements.txt" ]] || [[ -f "$project_dir/setup.py" ]]; then
        echo "python"
    elif [[ -f "$project_dir/package.json" ]]; then
        echo "node"
    elif [[ -f "$project_dir/go.mod" ]]; then
        echo "golang"
    else
        echo "unknown"
    fi
}

# Initialize development container
init_devcontainer() {
    local type="$1"
    local project_name="${2:-$(basename $(pwd))}"
    local project_dir="."
    
    log_info "Initializing $type development container for project: $project_name"
    
    # Validate template exists
    local template_dir="$TEMPLATES_DIR/$type"
    if [[ ! -d "$template_dir" ]]; then
        log_error "Template for '$type' not found: $template_dir"
        return 1
    fi
    
    # Create .devcontainer directory
    if [[ ! -d "$project_dir/.devcontainer" ]]; then
        mkdir -p "$project_dir/.devcontainer"
        log_success "Created .devcontainer directory"
    fi
    
    # Copy template files
    log_info "Copying template files..."
    cp -r "$template_dir"/* "$project_dir/.devcontainer/"
    
    # Create cache directory
    mkdir -p "$project_dir/.devcontainer/cache"
    mkdir -p "$project_dir/.devcontainer/jetbrains-settings"
    
    # Customize project name in docker-compose
    if [[ -f "$project_dir/.devcontainer/docker-compose.dev.yml" ]]; then
        sed -i.bak "s/\${PROJECT_NAME:-.*-project}/$project_name/g" "$project_dir/.devcontainer/docker-compose.dev.yml"
        rm "$project_dir/.devcontainer/docker-compose.dev.yml.bak"
    fi
    
    # Create .env file for project-specific settings
    cat > "$project_dir/.devcontainer/.env" << EOF
PROJECT_NAME=$project_name
COMPOSE_PROJECT_NAME=$project_name
ENVIRONMENT=development
EOF
    
    log_success "Development container initialized for $project_name"
    log_info "Next steps:"
    echo "  1. Review and customize .devcontainer/docker-compose.dev.yml"
    echo "  2. Start the environment: $0 start $project_name"
    echo "  3. Configure JetBrains IDE: $0 jetbrains-setup $project_name"
}

# Start development environment
start_environment() {
    local project_name="${1:-$(basename $(pwd))}"
    local rebuild="${2:-false}"
    local detach="${3:-true}"
    
    if [[ ! -f ".devcontainer/docker-compose.dev.yml" ]]; then
        log_error "No development container configuration found. Run 'init' first."
        return 1
    fi
    
    log_info "Starting development environment for: $project_name"
    
    local compose_args="-f .devcontainer/docker-compose.dev.yml"
    local up_args="up"
    
    if [[ "$rebuild" == "true" ]]; then
        up_args="$up_args --build"
    fi
    
    if [[ "$detach" == "true" ]]; then
        up_args="$up_args -d"
    fi
    
    cd .devcontainer
    docker-compose $compose_args $up_args
    cd ..
    
    if [[ "$detach" == "true" ]]; then
        log_success "Development environment started in background"
        log_info "Access the container: $0 shell $project_name"
        log_info "View logs: $0 logs $project_name"
    fi
}

# Stop development environment
stop_environment() {
    local project_name="${1:-$(basename $(pwd))}"
    
    if [[ ! -f ".devcontainer/docker-compose.dev.yml" ]]; then
        log_error "No development container configuration found."
        return 1
    fi
    
    log_info "Stopping development environment for: $project_name"
    
    cd .devcontainer
    docker-compose -f docker-compose.dev.yml down
    cd ..
    
    log_success "Development environment stopped"
}

# Show environment status
show_status() {
    local project_name="${1:-$(basename $(pwd))}"
    
    if [[ ! -f ".devcontainer/docker-compose.dev.yml" ]]; then
        log_error "No development container configuration found."
        return 1
    fi
    
    log_info "Development environment status for: $project_name"
    echo
    
    cd .devcontainer
    docker-compose -f docker-compose.dev.yml ps
    cd ..
}

# Open shell in container
open_shell() {
    local project_name="${1:-$(basename $(pwd))}"
    local service_name="${2:-${project_name}-dev}"
    
    log_info "Opening shell in development container: $service_name"
    
    cd .devcontainer
    docker-compose -f docker-compose.dev.yml exec "$service_name" bash
    cd ..
}

# Show logs
show_logs() {
    local project_name="${1:-$(basename $(pwd))}"
    local follow="${2:-false}"
    
    if [[ ! -f ".devcontainer/docker-compose.dev.yml" ]]; then
        log_error "No development container configuration found."
        return 1
    fi
    
    local logs_args="logs"
    if [[ "$follow" == "true" ]]; then
        logs_args="$logs_args -f"
    fi
    
    cd .devcontainer
    docker-compose -f docker-compose.dev.yml $logs_args
    cd ..
}

# Configure JetBrains IDE integration
setup_jetbrains() {
    local project_name="${1:-$(basename $(pwd))}"
    
    log_info "Setting up JetBrains IDE integration for: $project_name"
    
    # Detect project type for specific instructions
    local project_type=$(detect_project_type ".")
    
    echo
    log_info "JetBrains IDE Setup Instructions:"
    echo
    
    case "$project_type" in
        "python")
            echo "📝 PyCharm Setup:"
            echo "  1. Open PyCharm and select 'Open Project'"
            echo "  2. Navigate to this project directory"
            echo "  3. Go to Settings → Project → Python Interpreter"
            echo "  4. Click 'Add Interpreter' → 'Docker Compose'"
            echo "  5. Select docker-compose.dev.yml and service: ${project_name}-dev"
            echo "  6. Set Python interpreter path: /usr/local/bin/python"
            ;;
        "node")
            echo "📝 WebStorm Setup:"
            echo "  1. Open WebStorm and select 'Open Project'"
            echo "  2. Navigate to this project directory"
            echo "  3. Go to Settings → Languages & Frameworks → Node.js"
            echo "  4. Set Node interpreter to Docker Compose"
            echo "  5. Select docker-compose.dev.yml and service: ${project_name}-dev"
            echo "  6. Set Node.js interpreter path: /usr/local/bin/node"
            ;;
        "golang")
            echo "📝 GoLand Setup:"
            echo "  1. Open GoLand and select 'Open Project'"
            echo "  2. Navigate to this project directory"
            echo "  3. Go to Settings → Go → GOROOT"
            echo "  4. Set GOROOT to Docker Compose"
            echo "  5. Select docker-compose.dev.yml and service: ${project_name}-dev"
            echo "  6. Set Go interpreter path: /usr/local/go/bin/go"
            ;;
    esac
    
    echo
    echo "🔧 General Configuration:"
    echo "  • Remote development is configured automatically"
    echo "  • Debugger ports are exposed and ready"
    echo "  • File synchronization is optimized for performance"
    echo "  • IDE settings are persisted in .devcontainer/jetbrains-settings/"
    echo
    echo "🚀 Quick Start:"
    echo "  • Container is running at: ${project_name}-dev"
    echo "  • Application port: 8000 (Python) / 3000 (Node.js) / 8080 (Go)"
    echo "  • Debug port: 5678 (Python) / 9229 (Node.js) / 2345 (Go)"
    echo
}

# Clean up development environment
clean_environment() {
    local project_name="${1:-$(basename $(pwd))}"
    
    log_warning "Cleaning up development environment for: $project_name"
    read -p "This will remove containers, volumes, and networks. Continue? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cd .devcontainer
        docker-compose -f docker-compose.dev.yml down -v --remove-orphans
        docker system prune -f
        cd ..
        
        log_success "Development environment cleaned up"
    else
        log_info "Cleanup cancelled"
    fi
}

# List available templates
list_templates() {
    log_info "Available development container templates:"
    echo
    
    for template_dir in "$TEMPLATES_DIR"/*; do
        if [[ -d "$template_dir" ]]; then
            local type=$(basename "$template_dir")
            echo "📦 $type"
            
            if [[ -f "$template_dir/.devcontainer.json" ]]; then
                echo "   ✅ VS Code dev container support"
            fi
            
            if [[ -f "$template_dir/docker-compose.dev.yml" ]]; then
                echo "   ✅ JetBrains IDE integration"
            fi
            
            if [[ -d "$template_dir/jetbrains-settings" ]]; then
                echo "   ✅ Pre-configured IDE settings"
            fi
            
            echo
        fi
    done
}

# Main function
main() {
    local rebuild=false
    local no_cache=false
    local detach=true
    local follow=false
    
    # Parse global options
    while [[ $# -gt 0 ]]; do
        case $1 in
            --rebuild) rebuild=true; shift ;;
            --no-cache) no_cache=true; shift ;;
            --detach) detach=true; shift ;;
            --follow) follow=true; shift ;;
            --help|-h) show_usage; exit 0 ;;
            *) break ;;
        esac
    done
    
    case "${1:-help}" in
        "init")
            if [[ $# -lt 2 ]]; then
                log_error "Container type required"
                show_usage
                exit 1
            fi
            init_devcontainer "$2" "${3:-}"
            ;;
        "start")
            start_environment "${2:-}" "$rebuild" "$detach"
            ;;
        "stop")
            stop_environment "${2:-}"
            ;;
        "restart")
            stop_environment "${2:-}"
            start_environment "${2:-}" "$rebuild" "$detach"
            ;;
        "status")
            show_status "${2:-}"
            ;;
        "shell")
            open_shell "${2:-}"
            ;;
        "logs")
            show_logs "${2:-}" "$follow"
            ;;
        "jetbrains-setup")
            setup_jetbrains "${2:-}"
            ;;
        "clean")
            clean_environment "${2:-}"
            ;;
        "list")
            list_templates
            ;;
        "help"|"--help"|"-h")
            show_usage
            ;;
        *)
            log_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
