{"runtimes": {"nvidia": {"args": [], "path": "nvidia-container-runtime"}}, "experimental": false, "ip-forward": true, "ipv6": false, "ip6tables": false, "default-ulimits": {"memlock": {"Hard": 12884901888, "Name": "memlock", "Soft": 12884901888}, "nofile": {"Hard": 65536, "Name": "nofile", "Soft": 65536}}, "features": {"buildkit": true}, "log-driver": "json-file", "log-opts": {"max-size": "100m", "max-file": "3", "compress": "true"}, "storage-driver": "overlay2", "userland-proxy": false, "iptables": true, "max-concurrent-downloads": 6, "max-concurrent-uploads": 5, "max-download-attempts": 3, "icc": false, "no-new-privileges": true, "live-restore": true, "init": true}