{"runtimes": {"nvidia": {"args": [], "path": "nvidia-container-runtime"}}, "experimental": false, "ip-forward": true, "ipv6": false, "ip6tables": false, "features": {"buildkit": true}, "log-driver": "json-file", "log-opts": {"max-size": "100m", "max-file": "3", "compress": "true"}, "max-concurrent-downloads": 6, "max-concurrent-uploads": 5, "max-download-attempts": 3, "userland-proxy": false, "live-restore": true, "init": true}