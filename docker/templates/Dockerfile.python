# Multi-stage Python Dockerfile Template
# Optimized for BuildKit and fast development builds
# 
# Usage:
#   docker build --target development .  # For development
#   docker build --target production .   # For production
#
# Performance Features:
# - BuildKit cache mounts for pip/poetry
# - Multi-stage builds for smaller final images
# - Optimized layer ordering for cache efficiency
# - Parallel dependency installation

# =============================================================================
# Base Stage - Shared dependencies
# =============================================================================
FROM python:3.12-slim AS base

# Install system dependencies that rarely change
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd --gid 1000 appuser && \
    useradd --uid 1000 --gid appuser --shell /bin/bash --create-home appuser

# Set working directory
WORKDIR /app

# =============================================================================
# Dependencies Stage - Package installation with cache mounts
# =============================================================================
FROM base AS dependencies

# Copy dependency files first (for better caching)
# Handle different dependency management approaches
COPY requirements*.txt* ./
COPY pyproject.toml* poetry.lock* ./

# Install Poetry if pyproject.toml exists
RUN if [ -f pyproject.toml ]; then \
    pip install poetry && \
    poetry config virtualenvs.create false; \
    fi

# Install dependencies with cache mounts for better performance
# This uses BuildKit cache mounts to persist pip/poetry cache between builds
RUN --mount=type=cache,target=/root/.cache/pip \
    --mount=type=cache,target=/root/.cache/pypoetry \
    if [ -f pyproject.toml ]; then \
        poetry install --no-dev --no-interaction --no-ansi; \
    elif [ -f requirements.txt ]; then \
        pip install -r requirements.txt; \
    else \
        echo "No dependency files found, skipping dependency installation"; \
    fi

# =============================================================================
# Development Stage - Full development environment
# =============================================================================
FROM dependencies AS development

# Install development dependencies
RUN --mount=type=cache,target=/root/.cache/pip \
    --mount=type=cache,target=/root/.cache/pypoetry \
    if [ -f pyproject.toml ]; then \
        poetry install --no-interaction --no-ansi; \
    elif [ -f requirements-dev.txt ]; then \
        pip install -r requirements-dev.txt; \
    else \
        echo "No development dependencies found, skipping"; \
    fi

# Install common development tools
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install \
    pytest \
    black \
    flake8 \
    mypy \
    ipython

# Copy source code (this layer will invalidate frequently during development)
COPY --chown=appuser:appuser . .

# Switch to non-root user
USER appuser

# Development server command (override as needed)
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# =============================================================================
# Production Stage - Minimal production image
# =============================================================================
FROM dependencies AS production

# Copy only necessary files for production
COPY --chown=appuser:appuser . .

# Remove unnecessary files for production
RUN find . -type d -name "__pycache__" -exec rm -rf {} + && \
    find . -type d -name "*.egg-info" -exec rm -rf {} + && \
    find . -type f -name "*.pyc" -delete && \
    rm -rf .git .pytest_cache .coverage tests/ docs/

# Switch to non-root user
USER appuser

# Health check (customize as needed)
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Production command (customize as needed)
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]

# =============================================================================
# Testing Stage - For CI/CD pipelines
# =============================================================================
FROM development AS testing

# Run tests during build (optional)
RUN if [ -f pytest.ini ] || [ -f pyproject.toml ]; then \
        python -m pytest --tb=short; \
    fi

# =============================================================================
# Build Arguments and Labels
# =============================================================================
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION

LABEL maintainer="<EMAIL>" \
      org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="python-app" \
      org.label-schema.description="Python application" \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.version=$VERSION \
      org.label-schema.schema-version="1.0"

# =============================================================================
# Usage Examples:
# 
# Development build:
#   docker build --target development -t myapp:dev .
#   docker run -v $(pwd):/app -p 8000:8000 myapp:dev
#
# Production build:
#   docker build --target production -t myapp:prod .
#   docker run -p 8000:8000 myapp:prod
#
# Testing build:
#   docker build --target testing -t myapp:test .
#
# With build args:
#   docker build --target production \
#     --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
#     --build-arg VCS_REF=$(git rev-parse --short HEAD) \
#     --build-arg VERSION=1.0.0 \
#     -t myapp:1.0.0 .
# =============================================================================
