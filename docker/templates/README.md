# Docker Build Performance Templates

This directory contains optimized Dockerfile templates and build patterns for different technology stacks, designed to maximize build performance on multi-core development systems.

## 🎯 Template Goals

- **Fast Builds**: Optimized layer caching and ordering
- **Multi-stage Efficiency**: Separate dev/prod targets
- **Cache Mount Optimization**: Language-specific dependency caching
- **Minimal Rebuilds**: Smart layer invalidation patterns

## 📁 Template Structure

```
templates/
├── README.md                    # This file
├── Dockerfile.python           # Python projects (pip/poetry)
├── Dockerfile.node             # Node.js projects (npm/yarn/pnpm)
├── Dockerfile.golang           # Go projects
├── Dockerfile.rust             # Rust projects (cargo)
├── .dockerignore.python        # Python-specific ignore patterns
├── .dockerignore.node          # Node.js-specific ignore patterns
├── .dockerignore.golang        # Go-specific ignore patterns
├── .dockerignore.rust          # Rust-specific ignore patterns
├── .dockerignore.generic       # Generic ignore patterns
└── examples/                   # Example projects for testing
    ├── python-example/
    ├── node-example/
    └── golang-example/
```

## 🚀 Usage

### Apply Template to New Project

```bash
# Copy appropriate template
cp docker/templates/Dockerfile.python ./Dockerfile
cp docker/templates/.dockerignore.python ./.dockerignore

# Customize for your project
# Edit Dockerfile to match your specific requirements
```

### Test Template Performance

```bash
# Use the build optimizer script
./docker/build-optimizer.sh test python-example

# Or test manually
cd docker/templates/examples/python-example
docker build -t test-python .
```

## 📊 Performance Features

### **Layer Caching Strategy**
- Dependencies installed before source code copy
- Multi-stage builds with shared base layers
- BuildKit cache mounts for package managers

### **Build Optimization Patterns**
- Minimal base images (Alpine/slim variants)
- Efficient COPY operations
- Smart .dockerignore patterns
- Parallel build stages where possible

### **Development Workflow Integration**
- Hot reload support in dev targets
- Volume mounts for development
- Debug-friendly configurations

## 🔧 Customization Guide

Each template includes:
- **Base configuration** - Ready to use
- **Customization points** - Marked with comments
- **Performance notes** - Explaining optimization choices
- **Alternative approaches** - For different use cases

## 📈 Expected Performance Improvements

Based on optimization patterns:
- **50-80% faster** subsequent builds (cache hits)
- **30-50% smaller** final images (multi-stage)
- **Parallel dependency** installation where supported
- **Reduced layer** invalidation during development

---

*Templates optimized for BuildKit and 16-core development systems*
