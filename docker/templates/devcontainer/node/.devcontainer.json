{"name": "Node.js Development Container", "dockerComposeFile": "docker-compose.dev.yml", "service": "node-dev", "workspaceFolder": "/workspace", "features": {"ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/devcontainers/features/github-cli:1": {}, "ghcr.io/devcontainers/features/docker-in-docker:2": {"moby": true, "dockerDashComposeVersion": "v2"}}, "customizations": {"vscode": {"settings": {"typescript.preferences.quoteStyle": "single", "javascript.preferences.quoteStyle": "single", "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true, "source.organizeImports": true}, "eslint.workingDirectories": ["./"], "npm.packageManager": "auto", "typescript.updateImportsOnFileMove.enabled": "always", "javascript.updateImportsOnFileMove.enabled": "always", "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/*/**": true, "**/dist/**": true, "**/build/**": true, "**/.next/**": true, "**/.nuxt/**": true}}, "extensions": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-json", "christian-kohler.path-intellisense", "formulahendry.auto-rename-tag", "ms-vscode.test-adapter-converter", "hbenl.vscode-test-explorer", "ms-playwright.playwright"]}}, "forwardPorts": [3000, 3001, 8080, 9229, 9230], "portsAttributes": {"3000": {"label": "Application", "onAutoForward": "notify"}, "3001": {"label": "Dev Server", "onAutoForward": "notify"}, "8080": {"label": "Alternative Port", "onAutoForward": "silent"}, "9229": {"label": "Node Debugger", "onAutoForward": "silent"}, "9230": {"label": "Node Inspector", "onAutoForward": "silent"}}, "mounts": ["source=${localWorkspaceFolder}/.devcontainer/jetbrains-settings,target=/home/<USER>/.config/JetBrains,type=bind,consistency=cached", "source=${localWorkspaceFolder}/.devcontainer/cache,target=/home/<USER>/.cache,type=bind,consistency=delegated"], "remoteEnv": {"NODE_ENV": "development", "NPM_CONFIG_CACHE": "/home/<USER>/.cache/npm", "YARN_CACHE_FOLDER": "/home/<USER>/.cache/yarn", "PNPM_HOME": "/home/<USER>/.local/share/pnpm"}, "postCreateCommand": "npm install", "postStartCommand": "git config --global --add safe.directory /workspace", "remoteUser": "node", "shutdownAction": "stopCompose"}