{"name": "Python Development Container", "dockerComposeFile": "docker-compose.dev.yml", "service": "python-dev", "workspaceFolder": "/workspace", "features": {"ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/devcontainers/features/github-cli:1": {}, "ghcr.io/devcontainers/features/docker-in-docker:2": {"moby": true, "dockerDashComposeVersion": "v2"}}, "customizations": {"vscode": {"settings": {"python.defaultInterpreterPath": "/usr/local/bin/python", "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.formatting.provider": "black", "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.pytestArgs": ["tests"], "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/*/**": true, "**/__pycache__/**": true, "**/.pytest_cache/**": true}}, "extensions": ["ms-python.python", "ms-python.flake8", "ms-python.black-formatter", "ms-python.isort", "ms-python.pylint", "ms-toolsai.jupyter", "ms-vscode.test-adapter-converter", "hbenl.vscode-test-explorer"]}}, "forwardPorts": [8000, 5678, 9229], "portsAttributes": {"8000": {"label": "Application", "onAutoForward": "notify"}, "5678": {"label": "Python Debugger", "onAutoForward": "silent"}, "9229": {"label": "Node Debug", "onAutoForward": "silent"}}, "mounts": ["source=${localWorkspaceFolder}/.devcontainer/jetbrains-settings,target=/home/<USER>/.config/JetBrains,type=bind,consistency=cached", "source=${localWorkspaceFolder}/.devcontainer/cache,target=/home/<USER>/.cache,type=bind,consistency=delegated"], "remoteEnv": {"PYTHONPATH": "/workspace", "PYTHONDONTWRITEBYTECODE": "1", "PYTHONUNBUFFERED": "1", "PIP_CACHE_DIR": "/home/<USER>/.cache/pip", "POETRY_CACHE_DIR": "/home/<USER>/.cache/pypoetry"}, "postCreateCommand": "pip install --user -e .[dev]", "postStartCommand": "git config --global --add safe.directory /workspace", "remoteUser": "vscode", "shutdownAction": "stopCompose"}