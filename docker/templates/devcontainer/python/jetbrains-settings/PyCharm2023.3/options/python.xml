<?xml version="1.0" encoding="UTF-8"?>
<application>
  <component name="PyCharmProfessionalAdvertiser">
    <option name="shownTime" value="1703001600000" />
  </component>
  
  <component name="PythonSdkSettings">
    <option name="USE_NEW_ENVIRONMENT_FOR_NEW_PROJECT" value="false" />
    <option name="PREFERRED_VIRTUALIZATION_TOOL" value="POETRY" />
  </component>
  
  <component name="PythonDocumentationSettings">
    <option name="myDocStringFormat" value="Google" />
  </component>
  
  <component name="PyTestFrameworkService">
    <option name="SDK_TO_PYTEST" value="true" />
    <option name="SDK_TO_NOSETEST" value="false" />
    <option name="SDK_TO_UNITTESTS" value="false" />
  </component>
  
  <component name="PythonCompatibilityInspectionAdvertiser">
    <option name="version" value="3" />
  </component>
  
  <component name="PythonCommandLineSettings">
    <option name="INTERPRETER_OPTIONS" value="-u" />
    <option name="PARENT_ENVS" value="true" />
    <option name="SDK_HOME" value="/usr/local/bin/python" />
    <option name="WORKING_DIRECTORY" value="/workspace" />
    <option name="IS_MODULE_SDK" value="false" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
  </component>
  
  <component name="PyDebuggerSettings">
    <option name="watchReturnValues" value="true" />
    <option name="attachToSubprocess" value="true" />
    <option name="saveCallSignatures" value="true" />
    <option name="supportGeventDebugging" value="true" />
    <option name="supportQtDebugging" value="true" />
  </component>
  
  <component name="PythonScientificProjectSettings">
    <option name="enabled" value="true" />
    <option name="enabledInDataView" value="true" />
  </component>
</application>
