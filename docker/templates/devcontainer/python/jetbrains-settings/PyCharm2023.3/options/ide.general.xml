<?xml version="1.0" encoding="UTF-8"?>
<application>
  <component name="GeneralSettings">
    <option name="autoSaveFiles" value="true" />
    <option name="autoSaveIfInactive" value="true" />
    <option name="inactiveTimeout" value="15" />
    <option name="useSafeWrite" value="false" />
    <option name="useDefaultBrowser" value="true" />
    <option name="confirmExit" value="false" />
    <option name="confirmOpenNewProject2" value="0" />
    <option name="reopenLastProject" value="true" />
    <option name="supportScreenReaders" value="false" />
    <option name="showTipsOnStartup" value="false" />
  </component>
  
  <component name="Registry">
    <!-- Performance optimizations for remote development -->
    <entry key="ide.tooltip.initialDelay" value="1500" />
    <entry key="ide.tooltip.initialDelay.highlighter" value="1500" />
    <entry key="ide.tree.ui.experimental" value="true" />
    <entry key="ide.experimental.ui" value="false" />
    <entry key="ide.experimental.ui.inter.font" value="false" />
    
    <!-- File watching optimizations -->
    <entry key="ide.max.intellisense.filesize" value="5000" />
    <entry key="idea.max.content.load.filesize" value="20000" />
    <entry key="ide.file.contents.preload.limit" value="10000" />
    
    <!-- Remote development optimizations -->
    <entry key="ide.remote.fs.cache.enabled" value="true" />
    <entry key="ide.remote.fs.cache.size" value="1000" />
    <entry key="ide.remote.fs.refresh.period" value="5000" />
    
    <!-- Docker integration -->
    <entry key="docker.api.use.cli" value="true" />
    <entry key="docker.compose.wait.services.timeout" value="120" />
  </component>
</application>
