# Python Development Environment
# Optimized for JetBrains PyCharm and VS Code
version: '3.8'

services:
  python-dev:
    build:
      context: .
      dockerfile: .devcontainer/Dockerfile.dev
      args:
        PYTHON_VERSION: "3.12"
        NODE_VERSION: "18"
      cache_from:
        - python:3.12-slim
      target: development
    
    container_name: "${PROJECT_NAME:-python-project}-dev"
    
    volumes:
      # Source code with optimized consistency
      - type: bind
        source: .
        target: /workspace
        consistency: cached
      
      # IDE configuration persistence
      - type: bind
        source: ./.devcontainer/jetbrains-settings
        target: /home/<USER>/.config/JetBrains
        consistency: cached
      
      # Cache directories for performance
      - type: bind
        source: ./.devcontainer/cache
        target: /home/<USER>/.cache
        consistency: delegated
      
      # Git configuration
      - type: bind
        source: ~/.gitconfig
        target: /home/<USER>/.gitconfig
        read_only: true
      
      # SSH keys for git operations
      - type: bind
        source: ~/.ssh
        target: /home/<USER>/.ssh
        read_only: true
        consistency: cached
      
      # Docker socket for Docker-in-Docker
      - type: bind
        source: /var/run/docker.sock
        target: /var/run/docker.sock
    
    ports:
      # Application ports
      - "8000:8000"   # Main application
      - "8080:8080"   # Alternative app port
      
      # Development tool ports
      - "5678:5678"   # Python debugger (debugpy)
      - "9229:9229"   # Node.js debugger
      - "3000:3000"   # Frontend dev server
      
      # Database and service ports
      - "5432:5432"   # PostgreSQL
      - "6379:6379"   # Redis
      - "9200:9200"   # Elasticsearch
    
    environment:
      # Python configuration
      PYTHONPATH: /workspace
      PYTHONDONTWRITEBYTECODE: 1
      PYTHONUNBUFFERED: 1
      
      # Development environment
      ENVIRONMENT: development
      DEBUG: "true"
      
      # Cache directories
      PIP_CACHE_DIR: /home/<USER>/.cache/pip
      POETRY_CACHE_DIR: /home/<USER>/.cache/pypoetry
      NPM_CONFIG_CACHE: /home/<USER>/.cache/npm
      
      # JetBrains configuration
      PYCHARM_HOSTED: 1
      
      # Database connections (for local development)
      DATABASE_URL: ********************************************/devdb
      REDIS_URL: redis://redis:6379/0
      
      # Security (development only)
      SECRET_KEY: dev-secret-key-change-in-production
      
    working_dir: /workspace
    
    command: >
      bash -c "
        echo 'Starting Python development environment...' &&
        pip install --user -e .[dev] &&
        echo 'Development environment ready!' &&
        tail -f /dev/null
      "
    
    depends_on:
      - postgres
      - redis
    
    networks:
      - dev-network
    
    # Resource limits for development
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Supporting services
  postgres:
    image: postgres:15-alpine
    container_name: "${PROJECT_NAME:-python-project}-postgres"
    environment:
      POSTGRES_DB: devdb
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./.devcontainer/init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: "${PROJECT_NAME:-python-project}-redis"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Optional: Elasticsearch for search functionality
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: "${PROJECT_NAME:-python-project}-elasticsearch"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - dev-network
    profiles:
      - search

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
