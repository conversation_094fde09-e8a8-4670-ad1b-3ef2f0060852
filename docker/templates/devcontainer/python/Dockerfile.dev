# Development-Optimized Python Dockerfile
# Designed for JetBrains PyCharm and fast development workflows

ARG PYTHON_VERSION=3.12
ARG NODE_VERSION=18

# =============================================================================
# Base Development Image
# =============================================================================
FROM python:${PYTHON_VERSION}-slim AS base

# Install system dependencies for development
RUN apt-get update && apt-get install -y \
    # Build tools
    build-essential \
    gcc \
    g++ \
    make \
    cmake \
    # Development tools
    git \
    curl \
    wget \
    vim \
    nano \
    htop \
    tree \
    jq \
    # Network tools
    netcat-openbsd \
    telnet \
    # Database clients
    postgresql-client \
    # Additional utilities
    sudo \
    ssh \
    rsync \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js for frontend development
ARG NODE_VERSION
RUN curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash - \
    && apt-get install -y nodejs \
    && npm install -g npm@latest

# Create development user with sudo access
RUN groupadd --gid 1000 vscode \
    && useradd --uid 1000 --gid vscode --shell /bin/bash --create-home vscode \
    && echo 'vscode ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers

# =============================================================================
# Development Stage
# =============================================================================
FROM base AS development

# Switch to development user
USER vscode
WORKDIR /home/<USER>

# Create common directories
RUN mkdir -p \
    /home/<USER>/.cache/pip \
    /home/<USER>/.cache/pypoetry \
    /home/<USER>/.cache/npm \
    /home/<USER>/.config/JetBrains \
    /home/<USER>/.local/bin

# Install Python development tools
RUN --mount=type=cache,target=/home/<USER>/.cache/pip,uid=1000,gid=1000 \
    pip install --user --upgrade pip setuptools wheel && \
    pip install --user \
    # Core development tools
    poetry \
    pipenv \
    virtualenv \
    # Code quality tools
    black \
    isort \
    flake8 \
    pylint \
    mypy \
    bandit \
    # Testing tools
    pytest \
    pytest-cov \
    pytest-mock \
    pytest-xdist \
    # Debugging tools
    debugpy \
    ipdb \
    pdb++ \
    # Documentation tools
    sphinx \
    mkdocs \
    mkdocs-material \
    # Jupyter and data science
    jupyter \
    jupyterlab \
    ipython \
    # Development utilities
    pre-commit \
    tox \
    invoke \
    click \
    rich \
    typer

# Install Node.js development tools
RUN --mount=type=cache,target=/home/<USER>/.cache/npm,uid=1000,gid=1000 \
    npm install -g \
    # Frontend build tools
    webpack \
    webpack-cli \
    # Development servers
    live-server \
    http-server \
    # Code quality
    eslint \
    prettier \
    # Package managers
    yarn \
    pnpm

# Configure Git (will be overridden by mounted config)
RUN git config --global init.defaultBranch main \
    && git config --global pull.rebase false \
    && git config --global user.name "Developer" \
    && git config --global user.email "<EMAIL>"

# Configure Poetry
RUN poetry config virtualenvs.create false \
    && poetry config virtualenvs.in-project true

# Set up shell environment
RUN echo 'export PATH="/home/<USER>/.local/bin:$PATH"' >> /home/<USER>/.bashrc \
    && echo 'export PYTHONPATH="/workspace:$PYTHONPATH"' >> /home/<USER>/.bashrc \
    && echo 'alias ll="ls -la"' >> /home/<USER>/.bashrc \
    && echo 'alias la="ls -la"' >> /home/<USER>/.bashrc \
    && echo 'alias ..="cd .."' >> /home/<USER>/.bashrc

# Create workspace directory
RUN sudo mkdir -p /workspace \
    && sudo chown vscode:vscode /workspace

WORKDIR /workspace

# Expose common development ports
EXPOSE 8000 8080 3000 5678 9229

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

# Default command for development
CMD ["bash", "-c", "echo 'Python development environment ready!' && tail -f /dev/null"]

# =============================================================================
# JetBrains PyCharm Optimization
# =============================================================================

# Install JetBrains-specific tools
USER vscode
RUN --mount=type=cache,target=/home/<USER>/.cache/pip,uid=1000,gid=1000 \
    pip install --user \
    # PyCharm integration
    pycharm-community \
    # Remote development
    paramiko \
    # Code intelligence
    jedi \
    rope \
    # Performance profiling
    py-spy \
    memory-profiler \
    line-profiler

# Configure PyCharm settings directory
RUN mkdir -p /home/<USER>/.config/JetBrains/PyCharm2023.3/options

# Create PyCharm configuration template
COPY --chown=vscode:vscode jetbrains-settings/ /home/<USER>/.config/JetBrains/

# Set environment variables for JetBrains
ENV PYCHARM_HOSTED=1
ENV JETBRAINS_REMOTE_RUN=1

# =============================================================================
# Development Scripts
# =============================================================================

# Create development utility scripts
RUN mkdir -p /home/<USER>/.local/bin

# Hot reload script
RUN cat > /home/<USER>/.local/bin/hot-reload << 'EOF' && chmod +x /home/<USER>/.local/bin/hot-reload
#!/bin/bash
echo "Starting hot reload development server..."
if [ -f "manage.py" ]; then
    python manage.py runserver 0.0.0.0:8000
elif [ -f "app.py" ]; then
    python -m flask run --host=0.0.0.0 --port=8000 --debug
elif [ -f "main.py" ]; then
    python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
else
    echo "No recognized Python web framework found"
    echo "Available options: Django (manage.py), Flask (app.py), FastAPI (main.py)"
fi
EOF

# Debug server script
RUN cat > /home/<USER>/.local/bin/debug-server << 'EOF' && chmod +x /home/<USER>/.local/bin/debug-server
#!/bin/bash
echo "Starting debug server on port 5678..."
python -m debugpy --listen 0.0.0.0:5678 --wait-for-client "$@"
EOF

# Test runner script
RUN cat > /home/<USER>/.local/bin/run-tests << 'EOF' && chmod +x /home/<USER>/.local/bin/run-tests
#!/bin/bash
echo "Running tests with coverage..."
python -m pytest tests/ --cov=. --cov-report=html --cov-report=term-missing "$@"
EOF

# Final setup
WORKDIR /workspace
USER vscode
