# Go-specific .dockerignore template
# Optimized to reduce build context and improve build performance

# =============================================================================
# Go-specific files and directories
# =============================================================================

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out

# Dependency directories (vendor is optional)
vendor/

# Go workspace file
go.work

# =============================================================================
# Build outputs and generated files
# =============================================================================

# Build directories
build/
dist/
bin/
target/

# Generated files
*.pb.go
*.gen.go
*_generated.go

# =============================================================================
# Development and IDE files
# =============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# GoLand/IntelliJ
.idea/
*.iml

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# OS-specific files
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Version control
# =============================================================================
.git/
.gitignore
.gitattributes
.gitmodules

# =============================================================================
# Docker-related files
# =============================================================================
Dockerfile*
.dockerignore
docker-compose*.yml
docker-compose*.yaml

# =============================================================================
# CI/CD and deployment
# =============================================================================
.github/
.gitlab-ci.yml
.travis.yml
.circleci/
Jenkinsfile
.drone.yml
azure-pipelines.yml

# Kubernetes
k8s/
kubernetes/
*.yaml
*.yml

# Terraform
*.tf
*.tfstate
*.tfvars

# =============================================================================
# Documentation and README files
# =============================================================================
README*
CHANGELOG*
LICENSE*
CONTRIBUTING*
docs/
documentation/

# =============================================================================
# Logs and temporary files
# =============================================================================
*.log
logs/
tmp/
temp/

# =============================================================================
# Testing
# =============================================================================
coverage.html
coverage.xml
.coverage/
testdata/

# =============================================================================
# Security and secrets
# =============================================================================
.env*
secrets/
*.pem
*.key
*.crt
*.p12

# =============================================================================
# Go module files (keep what's needed)
# =============================================================================

# Keep go.mod and go.sum for dependency resolution
# go.mod - KEEP
# go.sum - KEEP

# But ignore module cache
.modcache/

# =============================================================================
# Air (hot reload) configuration
# =============================================================================
.air.toml
tmp/

# =============================================================================
# Performance and profiling
# =============================================================================
*.prof
profile.out
cpu.prof
mem.prof
block.prof
mutex.prof

# =============================================================================
# Custom additions (uncomment as needed)
# =============================================================================

# Configuration files (uncomment if sensitive)
# config/
# settings/
# *.conf
# *.ini
# *.toml
# *.yaml
# *.yml

# Data files (uncomment if you don't want to include data)
# data/
# datasets/
# *.json
# *.csv
# *.xml

# Media files (uncomment if you don't want to include media)
# static/
# assets/
# *.jpg
# *.jpeg
# *.png
# *.gif
# *.svg
# *.mp4
# *.avi

# Database files
# *.db
# *.sqlite
# *.sqlite3
