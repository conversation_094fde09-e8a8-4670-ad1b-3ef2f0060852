# Python-specific .dockerignore template
# Optimized to reduce build context and improve build performance

# =============================================================================
# Python-specific files and directories
# =============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# =============================================================================
# Development and IDE files
# =============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# OS-specific files
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Version control
# =============================================================================
.git/
.gitignore
.gitattributes
.gitmodules

# =============================================================================
# Docker-related files
# =============================================================================
Dockerfile*
.dockerignore
docker-compose*.yml
docker-compose*.yaml

# =============================================================================
# CI/CD and deployment
# =============================================================================
.github/
.gitlab-ci.yml
.travis.yml
.circleci/
Jenkinsfile
.drone.yml
azure-pipelines.yml

# Kubernetes
k8s/
kubernetes/
*.yaml
*.yml

# Terraform
*.tf
*.tfstate
*.tfvars

# =============================================================================
# Documentation and README files
# =============================================================================
README*
CHANGELOG*
LICENSE*
CONTRIBUTING*
docs/
documentation/

# =============================================================================
# Logs and temporary files
# =============================================================================
*.log
logs/
tmp/
temp/

# =============================================================================
# Database files
# =============================================================================
*.db
*.sqlite
*.sqlite3

# =============================================================================
# Security and secrets
# =============================================================================
.env*
secrets/
*.pem
*.key
*.crt
*.p12

# =============================================================================
# Performance and profiling
# =============================================================================
.prof
*.prof
profile.out

# =============================================================================
# Custom additions (uncomment as needed)
# =============================================================================

# Data files (uncomment if you don't want to include data)
# data/
# datasets/
# *.csv
# *.json
# *.xml

# Media files (uncomment if you don't want to include media)
# media/
# static/
# *.jpg
# *.jpeg
# *.png
# *.gif
# *.svg
# *.mp4
# *.avi

# Configuration files (uncomment if sensitive)
# config/
# settings/
# *.conf
# *.ini
