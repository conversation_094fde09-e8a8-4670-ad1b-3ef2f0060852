# Node.js-specific .dockerignore template
# Optimized to reduce build context and improve build performance

# =============================================================================
# Node.js-specific files and directories
# =============================================================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# Snowpack dependency directory
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# =============================================================================
# Build outputs and generated files
# =============================================================================

# Build directories
build/
dist/
lib/
es/
umd/

# Webpack
.webpack/

# Rollup
.rollup.cache/

# Vite
.vite/

# SvelteKit
.svelte-kit/

# =============================================================================
# Development and IDE files
# =============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# WebStorm/IntelliJ
.idea/
*.iml

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# OS-specific files
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Version control
# =============================================================================
.git/
.gitignore
.gitattributes
.gitmodules

# =============================================================================
# Docker-related files
# =============================================================================
Dockerfile*
.dockerignore
docker-compose*.yml
docker-compose*.yaml

# =============================================================================
# CI/CD and deployment
# =============================================================================
.github/
.gitlab-ci.yml
.travis.yml
.circleci/
Jenkinsfile
.drone.yml
azure-pipelines.yml

# Kubernetes
k8s/
kubernetes/
*.yaml
*.yml

# Terraform
*.tf
*.tfstate
*.tfvars

# =============================================================================
# Documentation and README files
# =============================================================================
README*
CHANGELOG*
LICENSE*
CONTRIBUTING*
docs/
documentation/

# =============================================================================
# Logs and temporary files
# =============================================================================
*.log
logs/
tmp/
temp/

# =============================================================================
# Testing
# =============================================================================
.jest/
jest.config.*
cypress/
.cypress/
playwright-report/
test-results/

# =============================================================================
# Security and secrets
# =============================================================================
.env*
secrets/
*.pem
*.key
*.crt
*.p12

# =============================================================================
# Package manager files (keep only what's needed)
# =============================================================================

# Keep package.json and lock files for dependency installation
# package.json - KEEP
# package-lock.json - KEEP
# yarn.lock - KEEP
# pnpm-lock.yaml - KEEP

# But ignore other package manager artifacts
.npmrc
.yarnrc
.yarnrc.yml

# =============================================================================
# Custom additions (uncomment as needed)
# =============================================================================

# Source maps (uncomment if you don't want to include them)
# *.map

# Static assets (uncomment if you don't want to include them)
# public/
# static/
# assets/

# Media files (uncomment if you don't want to include media)
# *.jpg
# *.jpeg
# *.png
# *.gif
# *.svg
# *.mp4
# *.avi

# Configuration files (uncomment if sensitive)
# config/
# settings/
# *.conf
# *.ini
