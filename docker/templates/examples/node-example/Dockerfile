# Multi-stage Node.js Dockerfile Template
# Optimized for BuildKit and fast development builds
# Supports npm, yarn, and pnpm package managers
#
# Usage:
#   docker build --target development .  # For development
#   docker build --target production .   # For production
#
# Performance Features:
# - BuildKit cache mounts for npm/yarn/pnpm
# - Multi-stage builds for smaller final images
# - Optimized layer ordering for cache efficiency
# - Package manager auto-detection

# =============================================================================
# Base Stage - Shared dependencies
# =============================================================================
FROM node:18-alpine as base

# Install system dependencies
RUN apk add --no-cache \
    git \
    curl \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1000 appuser && \
    adduser -u 1000 -G appuser -s /bin/sh -D appuser

# Set working directory
WORKDIR /app

# =============================================================================
# Dependencies Stage - Package installation with cache mounts
# =============================================================================
FROM base as dependencies

# Copy package files first (for better caching)
COPY package*.json yarn.lock* pnpm-lock.yaml* ./

# Install dependencies with cache mounts for better performance
# Auto-detect package manager and use appropriate cache
RUN --mount=type=cache,target=/root/.npm \
    --mount=type=cache,target=/root/.yarn \
    --mount=type=cache,target=/root/.local/share/pnpm \
    if [ -f pnpm-lock.yaml ]; then \
        corepack enable pnpm && \
        pnpm install --frozen-lockfile --prod; \
    elif [ -f yarn.lock ]; then \
        yarn install --frozen-lockfile --production; \
    else \
        npm ci --only=production; \
    fi

# =============================================================================
# Development Stage - Full development environment
# =============================================================================
FROM dependencies as development

# Install development dependencies
RUN --mount=type=cache,target=/root/.npm \
    --mount=type=cache,target=/root/.yarn \
    --mount=type=cache,target=/root/.local/share/pnpm \
    if [ -f pnpm-lock.yaml ]; then \
        pnpm install --frozen-lockfile; \
    elif [ -f yarn.lock ]; then \
        yarn install --frozen-lockfile; \
    else \
        npm ci; \
    fi

# Copy source code (this layer will invalidate frequently during development)
COPY --chown=appuser:appuser . .

# Switch to non-root user
USER appuser

# Expose common development port
EXPOSE 3000

# Development command with hot reload
CMD ["npm", "run", "dev"]

# =============================================================================
# Build Stage - Application build
# =============================================================================
FROM development as build

# Build the application
RUN if [ -f pnpm-lock.yaml ]; then \
        pnpm run build; \
    elif [ -f yarn.lock ]; then \
        yarn build; \
    else \
        npm run build; \
    fi

# =============================================================================
# Production Stage - Minimal production image
# =============================================================================
FROM base as production

# Copy production dependencies from dependencies stage
COPY --from=dependencies --chown=appuser:appuser /app/node_modules ./node_modules

# Copy built application from build stage
COPY --from=build --chown=appuser:appuser /app/dist ./dist
COPY --from=build --chown=appuser:appuser /app/build ./build

# Copy necessary files
COPY --chown=appuser:appuser package*.json ./
COPY --chown=appuser:appuser public ./public

# Switch to non-root user
USER appuser

# Expose application port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Production command
CMD ["npm", "start"]

# =============================================================================
# Testing Stage - For CI/CD pipelines
# =============================================================================
FROM development as testing

# Run tests during build
RUN npm test

# Run linting
RUN npm run lint || true

# =============================================================================
# Nginx Stage - Static file serving (for SPAs)
# =============================================================================
FROM nginx:alpine as nginx

# Copy built static files
COPY --from=build /app/dist /usr/share/nginx/html
COPY --from=build /app/build /usr/share/nginx/html

# Copy custom nginx configuration if it exists
COPY nginx.conf* /etc/nginx/conf.d/

# Expose port
EXPOSE 80

# Health check for nginx
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# =============================================================================
# Build Arguments and Labels
# =============================================================================
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION
ARG NODE_ENV=production

ENV NODE_ENV=$NODE_ENV

LABEL maintainer="<EMAIL>" \
      org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="node-app" \
      org.label-schema.description="Node.js application" \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.version=$VERSION \
      org.label-schema.schema-version="1.0"

# =============================================================================
# Usage Examples:
# 
# Development build:
#   docker build --target development -t myapp:dev .
#   docker run -v $(pwd):/app -p 3000:3000 myapp:dev
#
# Production build:
#   docker build --target production -t myapp:prod .
#   docker run -p 3000:3000 myapp:prod
#
# Static file serving (SPA):
#   docker build --target nginx -t myapp:static .
#   docker run -p 80:80 myapp:static
#
# Testing build:
#   docker build --target testing -t myapp:test .
#
# With specific package manager:
#   docker build --target production -t myapp:prod .  # Auto-detects
#
# With build args:
#   docker build --target production \
#     --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
#     --build-arg VCS_REF=$(git rev-parse --short HEAD) \
#     --build-arg VERSION=1.0.0 \
#     --build-arg NODE_ENV=production \
#     -t myapp:1.0.0 .
# =============================================================================
