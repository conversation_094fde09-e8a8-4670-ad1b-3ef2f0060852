# Multi-stage Go Dockerfile Template
# Optimized for BuildKit and fast development builds
#
# Usage:
#   docker build --target development .  # For development
#   docker build --target production .   # For production
#
# Performance Features:
# - BuildKit cache mounts for Go modules
# - Multi-stage builds for minimal final images
# - Optimized layer ordering for cache efficiency
# - C<PERSON><PERSON> disabled for static binaries

# =============================================================================
# Base Stage - Build environment
# =============================================================================
FROM golang:1.21-alpine as base

# Install build dependencies
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# =============================================================================
# Dependencies Stage - Module download with cache mounts
# =============================================================================
FROM base as dependencies

# Copy go mod files first (for better caching)
COPY go.mod go.sum ./

# Download dependencies with cache mount for better performance
RUN --mount=type=cache,target=/go/pkg/mod \
    go mod download && \
    go mod verify

# =============================================================================
# Development Stage - Full development environment
# =============================================================================
FROM dependencies as development

# Install development tools
RUN --mount=type=cache,target=/go/pkg/mod \
    go install github.com/cosmtrek/air@latest && \
    go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# Copy source code
COPY . .

# Build for development (with debug info)
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    go build -o /app/bin/app .

# Expose common development port
EXPOSE 8080

# Development command with hot reload
CMD ["air", "-c", ".air.toml"]

# =============================================================================
# Build Stage - Optimized build
# =============================================================================
FROM dependencies as build

# Copy source code
COPY . .

# Build optimized binary
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 \
    go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o /app/bin/app .

# =============================================================================
# Production Stage - Minimal production image
# =============================================================================
FROM scratch as production

# Copy CA certificates for HTTPS requests
COPY --from=base /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Copy timezone data
COPY --from=base /usr/share/zoneinfo /usr/share/zoneinfo

# Copy the binary
COPY --from=build /app/bin/app /app

# Expose application port
EXPOSE 8080

# Health check (if your app supports it)
# HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
#     CMD ["/app", "health"] || exit 1

# Run the binary
ENTRYPOINT ["/app"]

# =============================================================================
# Testing Stage - For CI/CD pipelines
# =============================================================================
FROM dependencies as testing

# Copy source code
COPY . .

# Run tests
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    go test -v ./...

# Run linting
RUN --mount=type=cache,target=/go/pkg/mod \
    golangci-lint run

# Run security checks
RUN --mount=type=cache,target=/go/pkg/mod \
    go list -json -m all | nancy sleuth

# =============================================================================
# Alpine Production Stage - Slightly larger but more compatible
# =============================================================================
FROM alpine:latest as alpine-production

# Install runtime dependencies
RUN apk --no-cache add ca-certificates tzdata

# Create non-root user
RUN addgroup -g 1000 appuser && \
    adduser -u 1000 -G appuser -s /bin/sh -D appuser

# Set working directory
WORKDIR /app

# Copy the binary
COPY --from=build --chown=appuser:appuser /app/bin/app .

# Switch to non-root user
USER appuser

# Expose application port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD ./app health || exit 1

# Run the binary
CMD ["./app"]

# =============================================================================
# Build Arguments and Labels
# =============================================================================
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION

LABEL maintainer="<EMAIL>" \
      org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="go-app" \
      org.label-schema.description="Go application" \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.version=$VERSION \
      org.label-schema.schema-version="1.0"

# =============================================================================
# Usage Examples:
# 
# Development build:
#   docker build --target development -t myapp:dev .
#   docker run -v $(pwd):/app -p 8080:8080 myapp:dev
#
# Production build (minimal):
#   docker build --target production -t myapp:prod .
#   docker run -p 8080:8080 myapp:prod
#
# Production build (Alpine):
#   docker build --target alpine-production -t myapp:alpine .
#   docker run -p 8080:8080 myapp:alpine
#
# Testing build:
#   docker build --target testing -t myapp:test .
#
# With build args:
#   docker build --target production \
#     --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
#     --build-arg VCS_REF=$(git rev-parse --short HEAD) \
#     --build-arg VERSION=1.0.0 \
#     -t myapp:1.0.0 .
# =============================================================================
