#!/bin/bash

# Docker Engine Optimization Script
# Applies performance optimizations for 16-core development system

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root. Use sudo when prompted."
        exit 1
    fi
}

# Backup current configuration
backup_config() {
    local backup_file="/etc/docker/daemon.json.pre-optimization-$(date +%Y%m%d-%H%M%S)"
    
    log_info "Creating backup of current Docker configuration..."
    
    if sudo cp /etc/docker/daemon.json "$backup_file"; then
        log_success "Backup created: $backup_file"
        echo "$backup_file" > /tmp/docker-backup-location
    else
        log_error "Failed to create backup"
        exit 1
    fi
}

# Validate JSON configuration
validate_config() {
    local config_file="$1"
    
    log_info "Validating Docker configuration..."
    
    # Remove JSON comments for validation
    if python3 -c "
import json
import re

with open('$config_file', 'r') as f:
    content = f.read()

# Remove JSON comments (lines starting with //)
lines = content.split('\n')
cleaned_lines = []
for line in lines:
    if not re.match(r'^\s*\"//.*\":', line.strip()):
        cleaned_lines.append(line)

cleaned_content = '\n'.join(cleaned_lines)

try:
    json.loads(cleaned_content)
    print('Valid JSON configuration')
except json.JSONDecodeError as e:
    print(f'Invalid JSON: {e}')
    exit(1)
" 2>/dev/null; then
        log_success "Configuration is valid JSON"
        return 0
    else
        log_error "Configuration contains invalid JSON"
        return 1
    fi
}

# Apply optimized configuration
apply_config() {
    local source_config="$(dirname "$0")/daemon-optimized.json"
    
    log_info "Applying optimized Docker configuration..."
    
    if [[ ! -f "$source_config" ]]; then
        log_error "Optimized configuration file not found: $source_config"
        exit 1
    fi
    
    # Validate before applying
    if ! validate_config "$source_config"; then
        log_error "Configuration validation failed"
        exit 1
    fi
    
    # Apply configuration
    if sudo cp "$source_config" /etc/docker/daemon.json; then
        log_success "Configuration applied successfully"
    else
        log_error "Failed to apply configuration"
        exit 1
    fi
}

# Restart Docker service
restart_docker() {
    log_info "Restarting Docker service..."
    
    if sudo systemctl restart docker; then
        log_success "Docker service restarted"
        
        # Wait for Docker to be ready
        log_info "Waiting for Docker to be ready..."
        sleep 5
        
        if docker info >/dev/null 2>&1; then
            log_success "Docker is ready"
        else
            log_error "Docker failed to start properly"
            return 1
        fi
    else
        log_error "Failed to restart Docker service"
        return 1
    fi
}

# Rollback configuration if needed
rollback_config() {
    local backup_file
    
    if [[ -f /tmp/docker-backup-location ]]; then
        backup_file=$(cat /tmp/docker-backup-location)
        
        log_warning "Rolling back to previous configuration..."
        
        if sudo cp "$backup_file" /etc/docker/daemon.json; then
            log_info "Configuration rolled back"
            
            if sudo systemctl restart docker; then
                log_success "Docker service restarted with previous configuration"
            else
                log_error "Failed to restart Docker after rollback"
            fi
        else
            log_error "Failed to rollback configuration"
        fi
    else
        log_error "No backup location found for rollback"
    fi
}

# Test Docker functionality
test_docker() {
    log_info "Testing Docker functionality..."
    
    # Test basic Docker commands
    if docker version >/dev/null 2>&1; then
        log_success "Docker version command works"
    else
        log_error "Docker version command failed"
        return 1
    fi
    
    if docker info >/dev/null 2>&1; then
        log_success "Docker info command works"
    else
        log_error "Docker info command failed"
        return 1
    fi
    
    # Test BuildKit
    if docker buildx version >/dev/null 2>&1; then
        log_success "BuildKit is available"
    else
        log_warning "BuildKit may not be properly configured"
    fi
    
    log_success "Docker functionality test completed"
}

# Show optimization results
show_results() {
    log_info "Docker optimization results:"
    echo
    echo "=== Docker Info ==="
    docker info | grep -E "(CPUs|Total Memory|Server Version|Storage Driver|Logging Driver)"
    echo
    echo "=== BuildKit Status ==="
    docker buildx ls
    echo
    log_success "Docker engine optimization completed successfully!"
    echo
    log_info "Next steps:"
    echo "  1. Test build performance with your projects"
    echo "  2. Monitor resource usage during development"
    echo "  3. Proceed to Phase 2: Build Performance Enhancement"
}

# Main execution
main() {
    echo "🐳 Docker Engine Optimization"
    echo "============================="
    echo
    
    check_permissions
    
    # Trap for cleanup on failure
    trap 'log_error "Optimization failed. Run with --rollback to restore previous configuration."' ERR
    
    case "${1:-apply}" in
        "apply")
            backup_config
            apply_config
            restart_docker
            test_docker
            show_results
            ;;
        "rollback")
            rollback_config
            ;;
        "test")
            test_docker
            ;;
        *)
            echo "Usage: $0 [apply|rollback|test]"
            echo "  apply   - Apply Docker optimizations (default)"
            echo "  rollback - Restore previous configuration"
            echo "  test    - Test current Docker functionality"
            exit 1
            ;;
    esac
}

main "$@"
