#!/bin/bash

# Docker Performance Test Script
# Tests build performance and basic functionality

set -euo pipefail

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🐳 Docker Performance Test${NC}"
echo "=========================="
echo

# Test 1: BuildKit functionality
echo -e "${BLUE}Test 1: BuildKit Functionality${NC}"
if docker buildx version >/dev/null 2>&1; then
    echo "✅ BuildKit is available and working"
    docker buildx ls
else
    echo "❌ BuildKit is not working"
fi
echo

# Test 2: Concurrent downloads
echo -e "${BLUE}Test 2: Concurrent Download Performance${NC}"
echo "Testing concurrent downloads with small images..."
time {
    docker pull alpine:latest >/dev/null 2>&1 &
    docker pull busybox:latest >/dev/null 2>&1 &
    docker pull ubuntu:22.04 >/dev/null 2>&1 &
    wait
}
echo "✅ Concurrent downloads completed"
echo

# Test 3: Build performance test
echo -e "${BLUE}Test 3: Build Performance${NC}"
mkdir -p /tmp/docker-test
cat > /tmp/docker-test/Dockerfile << 'EOF'
FROM alpine:latest
RUN apk add --no-cache curl
RUN echo "Build test completed"
WORKDIR /app
COPY . .
CMD ["echo", "Hello from optimized Docker!"]
EOF

echo "test file" > /tmp/docker-test/test.txt

echo "Building test image..."
time docker build -t docker-test /tmp/docker-test >/dev/null 2>&1
echo "✅ Build completed successfully"

# Test 4: Container startup
echo -e "${BLUE}Test 4: Container Startup Performance${NC}"
echo "Testing container startup time..."
time docker run --rm docker-test >/dev/null 2>&1
echo "✅ Container startup completed"
echo

# Test 5: Resource usage
echo -e "${BLUE}Test 5: Resource Usage${NC}"
echo "Docker system resource usage:"
docker system df
echo

# Cleanup
echo -e "${BLUE}Cleanup${NC}"
docker rmi docker-test >/dev/null 2>&1
rm -rf /tmp/docker-test
echo "✅ Cleanup completed"
echo

echo -e "${GREEN}🎉 Docker performance test completed successfully!${NC}"
echo
echo "Key optimizations applied:"
echo "  ✅ BuildKit enabled for improved build performance"
echo "  ✅ Conservative concurrency (6 downloads, 5 uploads)"
echo "  ✅ Structured logging with compression"
echo "  ✅ Live restore enabled for better reliability"
echo "  ✅ Userland proxy disabled for better network performance"
