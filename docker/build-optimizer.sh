#!/bin/bash

# Docker Build Optimizer
# Utility script for applying templates and optimizing <PERSON><PERSON> builds

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEMPLATES_DIR="$SCRIPT_DIR/templates"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show usage information
show_usage() {
    cat << EOF
🐳 Docker Build Optimizer

Usage: $0 <command> [options]

Commands:
  apply <type> [target_dir]    Apply template to directory
  test <type>                  Test template performance
  analyze [target_dir]         Analyze existing Dockerfile
  list                         List available templates
  help                         Show this help message

Template Types:
  python                       Python projects (pip/poetry)
  node                         Node.js projects (npm/yarn/pnpm)
  golang                       Go projects
  rust                         Rust projects (cargo)

Examples:
  $0 apply python ./my-python-app
  $0 test node
  $0 analyze ./existing-project
  $0 list

Options:
  --dry-run                    Show what would be done without executing
  --force                      Overwrite existing files
  --backup                     Create backup of existing files

EOF
}

# List available templates
list_templates() {
    log_info "Available Docker templates:"
    echo
    
    for template in "$TEMPLATES_DIR"/Dockerfile.*; do
        if [[ -f "$template" ]]; then
            type=$(basename "$template" | sed 's/Dockerfile\.//')
            dockerignore="$TEMPLATES_DIR/.dockerignore.$type"
            
            echo "📦 $type"
            echo "   Dockerfile: $(basename "$template")"
            if [[ -f "$dockerignore" ]]; then
                echo "   .dockerignore: $(basename "$dockerignore")"
            else
                echo "   .dockerignore: ❌ Not available"
            fi
            echo
        fi
    done
}

# Detect project type
detect_project_type() {
    local target_dir="${1:-.}"
    
    if [[ -f "$target_dir/pyproject.toml" ]] || [[ -f "$target_dir/requirements.txt" ]] || [[ -f "$target_dir/setup.py" ]]; then
        echo "python"
    elif [[ -f "$target_dir/package.json" ]]; then
        echo "node"
    elif [[ -f "$target_dir/go.mod" ]]; then
        echo "golang"
    elif [[ -f "$target_dir/Cargo.toml" ]]; then
        echo "rust"
    else
        echo "unknown"
    fi
}

# Apply template to directory
apply_template() {
    local type="$1"
    local target_dir="${2:-.}"
    local dry_run="${3:-false}"
    local force="${4:-false}"
    local backup="${5:-false}"
    
    local dockerfile_template="$TEMPLATES_DIR/Dockerfile.$type"
    local dockerignore_template="$TEMPLATES_DIR/.dockerignore.$type"
    
    # Validate template exists
    if [[ ! -f "$dockerfile_template" ]]; then
        log_error "Template for '$type' not found: $dockerfile_template"
        return 1
    fi
    
    # Create target directory if it doesn't exist
    if [[ ! -d "$target_dir" ]]; then
        if [[ "$dry_run" == "true" ]]; then
            log_info "Would create directory: $target_dir"
        else
            mkdir -p "$target_dir"
            log_success "Created directory: $target_dir"
        fi
    fi
    
    # Handle Dockerfile
    local target_dockerfile="$target_dir/Dockerfile"
    if [[ -f "$target_dockerfile" ]]; then
        if [[ "$force" != "true" ]]; then
            log_warning "Dockerfile already exists: $target_dockerfile"
            read -p "Overwrite? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "Skipping Dockerfile"
                return 0
            fi
        fi
        
        if [[ "$backup" == "true" ]]; then
            local backup_file="$target_dockerfile.backup.$(date +%Y%m%d-%H%M%S)"
            if [[ "$dry_run" == "true" ]]; then
                log_info "Would backup: $target_dockerfile -> $backup_file"
            else
                cp "$target_dockerfile" "$backup_file"
                log_success "Backup created: $backup_file"
            fi
        fi
    fi
    
    if [[ "$dry_run" == "true" ]]; then
        log_info "Would copy: $dockerfile_template -> $target_dockerfile"
    else
        cp "$dockerfile_template" "$target_dockerfile"
        log_success "Applied Dockerfile template: $target_dockerfile"
    fi
    
    # Handle .dockerignore
    if [[ -f "$dockerignore_template" ]]; then
        local target_dockerignore="$target_dir/.dockerignore"
        
        if [[ -f "$target_dockerignore" ]] && [[ "$backup" == "true" ]]; then
            local backup_file="$target_dockerignore.backup.$(date +%Y%m%d-%H%M%S)"
            if [[ "$dry_run" == "true" ]]; then
                log_info "Would backup: $target_dockerignore -> $backup_file"
            else
                cp "$target_dockerignore" "$backup_file"
                log_success "Backup created: $backup_file"
            fi
        fi
        
        if [[ "$dry_run" == "true" ]]; then
            log_info "Would copy: $dockerignore_template -> $target_dockerignore"
        else
            cp "$dockerignore_template" "$target_dockerignore"
            log_success "Applied .dockerignore template: $target_dockerignore"
        fi
    fi
    
    # Show next steps
    if [[ "$dry_run" != "true" ]]; then
        echo
        log_info "Template applied successfully! Next steps:"
        echo "  1. Review and customize the Dockerfile for your project"
        echo "  2. Update .dockerignore if needed"
        echo "  3. Test the build: docker build --target development -t myapp:dev ."
        echo "  4. Run performance test: $0 test $type"
    fi
}

# Test template performance
test_template() {
    local type="$1"
    local test_dir="$TEMPLATES_DIR/examples/${type}-example"
    
    log_info "Testing $type template performance..."
    
    # Check if test example exists
    if [[ ! -d "$test_dir" ]]; then
        log_warning "Test example not found: $test_dir"
        log_info "Creating minimal test example..."
        create_test_example "$type" "$test_dir"
    fi
    
    cd "$test_dir"
    
    # Run build performance test
    log_info "Building development target..."
    time docker build --target development -t "test-$type:dev" . || {
        log_error "Development build failed"
        return 1
    }
    
    log_info "Building production target..."
    time docker build --target production -t "test-$type:prod" . || {
        log_error "Production build failed"
        return 1
    }
    
    # Show image sizes
    log_info "Image sizes:"
    docker images "test-$type" --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    # Cleanup
    log_info "Cleaning up test images..."
    docker rmi "test-$type:dev" "test-$type:prod" >/dev/null 2>&1 || true
    
    log_success "Template test completed"
}

# Create minimal test example
create_test_example() {
    local type="$1"
    local test_dir="$2"
    
    mkdir -p "$test_dir"
    
    case "$type" in
        "python")
            cat > "$test_dir/requirements.txt" << 'EOF'
fastapi==0.104.1
uvicorn==0.24.0
EOF
            cat > "$test_dir/main.py" << 'EOF'
from fastapi import FastAPI
app = FastAPI()

@app.get("/")
def read_root():
    return {"Hello": "World"}

@app.get("/health")
def health_check():
    return {"status": "healthy"}
EOF
            ;;
        "node")
            cat > "$test_dir/package.json" << 'EOF'
{
  "name": "test-app",
  "version": "1.0.0",
  "scripts": {
    "start": "node index.js",
    "dev": "node index.js"
  },
  "dependencies": {
    "express": "^4.18.2"
  }
}
EOF
            cat > "$test_dir/index.js" << 'EOF'
const express = require('express');
const app = express();
const port = 3000;

app.get('/', (req, res) => {
  res.json({ message: 'Hello World!' });
});

app.get('/health', (req, res) => {
  res.json({ status: 'healthy' });
});

app.listen(port, () => {
  console.log(`App listening at http://localhost:${port}`);
});
EOF
            ;;
        "golang")
            cat > "$test_dir/go.mod" << 'EOF'
module test-app

go 1.21
EOF
            cat > "$test_dir/main.go" << 'EOF'
package main

import (
    "encoding/json"
    "net/http"
)

func main() {
    http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
        json.NewEncoder(w).Encode(map[string]string{"message": "Hello World!"})
    })
    
    http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
        json.NewEncoder(w).Encode(map[string]string{"status": "healthy"})
    })
    
    http.ListenAndServe(":8080", nil)
}
EOF
            ;;
    esac
    
    # Copy appropriate templates
    cp "$TEMPLATES_DIR/Dockerfile.$type" "$test_dir/Dockerfile"
    if [[ -f "$TEMPLATES_DIR/.dockerignore.$type" ]]; then
        cp "$TEMPLATES_DIR/.dockerignore.$type" "$test_dir/.dockerignore"
    fi
}

# Analyze existing Dockerfile
analyze_dockerfile() {
    local target_dir="${1:-.}"
    local dockerfile="$target_dir/Dockerfile"
    
    if [[ ! -f "$dockerfile" ]]; then
        log_error "No Dockerfile found in: $target_dir"
        return 1
    fi
    
    log_info "Analyzing Dockerfile: $dockerfile"
    echo
    
    # Basic analysis
    local layers=$(grep -c "^RUN\|^COPY\|^ADD" "$dockerfile" || echo "0")
    local multistage=$(grep -c "^FROM.*as" "$dockerfile" || echo "0")
    local cache_mounts=$(grep -c "mount=type=cache" "$dockerfile" || echo "0")
    
    echo "📊 Dockerfile Analysis:"
    echo "  Layers (RUN/COPY/ADD): $layers"
    echo "  Multi-stage builds: $multistage"
    echo "  Cache mounts: $cache_mounts"
    echo
    
    # Optimization suggestions
    echo "💡 Optimization Suggestions:"
    
    if [[ $multistage -eq 0 ]]; then
        echo "  ⚠️  Consider using multi-stage builds for smaller images"
    fi
    
    if [[ $cache_mounts -eq 0 ]]; then
        echo "  ⚠️  Consider using BuildKit cache mounts for dependencies"
    fi
    
    if ! grep -q "\.dockerignore" "$target_dir/.dockerignore" 2>/dev/null; then
        echo "  ⚠️  Add .dockerignore to reduce build context"
    fi
    
    # Detect project type and suggest template
    local detected_type=$(detect_project_type "$target_dir")
    if [[ "$detected_type" != "unknown" ]]; then
        echo "  💡 Detected project type: $detected_type"
        echo "     Consider applying optimized template: $0 apply $detected_type $target_dir"
    fi
}

# Main function
main() {
    case "${1:-help}" in
        "apply")
            if [[ $# -lt 2 ]]; then
                log_error "Template type required"
                show_usage
                exit 1
            fi
            
            local type="$2"
            local target_dir="${3:-.}"
            local dry_run=false
            local force=false
            local backup=false
            
            # Parse additional options
            shift 2
            while [[ $# -gt 0 ]]; do
                case $1 in
                    --dry-run) dry_run=true ;;
                    --force) force=true ;;
                    --backup) backup=true ;;
                    *) target_dir="$1" ;;
                esac
                shift
            done
            
            apply_template "$type" "$target_dir" "$dry_run" "$force" "$backup"
            ;;
        "test")
            if [[ $# -lt 2 ]]; then
                log_error "Template type required"
                show_usage
                exit 1
            fi
            test_template "$2"
            ;;
        "analyze")
            analyze_dockerfile "${2:-.}"
            ;;
        "list")
            list_templates
            ;;
        "help"|"--help"|"-h")
            show_usage
            ;;
        *)
            log_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
